package com.focusflow.data.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0006\bg\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00040\u0003H\'J\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0004H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u0004H\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u000b\u001a\u00020\b2\u0006\u0010\f\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000eJ\u0016\u0010\u000f\u001a\u00020\b2\u0006\u0010\u0010\u001a\u00020\u0011H\u00a7@\u00a2\u0006\u0002\u0010\u0012J\u0016\u0010\u0013\u001a\u00020\b2\u0006\u0010\u0014\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000eJ\u0016\u0010\u0015\u001a\u00020\b2\u0006\u0010\u0010\u001a\u00020\u0011H\u00a7@\u00a2\u0006\u0002\u0010\u0012J\u0016\u0010\u0016\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u0004H\u00a7@\u00a2\u0006\u0002\u0010\n\u00a8\u0006\u0017"}, d2 = {"Lcom/focusflow/data/dao/UserPreferencesDao;", "", "getUserPreferences", "Lkotlinx/coroutines/flow/Flow;", "Lcom/focusflow/data/model/UserPreferences;", "getUserPreferencesSync", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertUserPreferences", "", "preferences", "(Lcom/focusflow/data/model/UserPreferences;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateBudgetPeriod", "period", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateDarkModeEnabled", "enabled", "", "(ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateFontSize", "fontSize", "updateNotificationsEnabled", "updateUserPreferences", "app_debug"})
@androidx.room.Dao
public abstract interface UserPreferencesDao {
    
    @androidx.room.Query(value = "SELECT * FROM user_preferences WHERE id = 1")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<com.focusflow.data.model.UserPreferences> getUserPreferences();
    
    @androidx.room.Query(value = "SELECT * FROM user_preferences WHERE id = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getUserPreferencesSync(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.UserPreferences> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertUserPreferences(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.UserPreferences preferences, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateUserPreferences(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.UserPreferences preferences, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE user_preferences SET budgetPeriod = :period WHERE id = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateBudgetPeriod(@org.jetbrains.annotations.NotNull
    java.lang.String period, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE user_preferences SET notificationsEnabled = :enabled WHERE id = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateNotificationsEnabled(boolean enabled, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE user_preferences SET darkModeEnabled = :enabled WHERE id = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateDarkModeEnabled(boolean enabled, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE user_preferences SET fontSize = :fontSize WHERE id = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateFontSize(@org.jetbrains.annotations.NotNull
    java.lang.String fontSize, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}