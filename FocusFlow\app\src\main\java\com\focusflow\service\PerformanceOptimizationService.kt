package com.focusflow.service

import android.content.Context
import androidx.room.Room
import androidx.work.*
import com.focusflow.data.database.FocusFlowDatabase
import com.focusflow.data.model.*
import com.focusflow.utils.ErrorHandler
import com.focusflow.utils.PerformanceMonitor
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class PerformanceOptimizationService @Inject constructor(
    @ApplicationContext private val context: Context,
    private val database: FocusFlowDatabase
) {
    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val cache = mutableMapOf<String, CacheEntry<Any>>()
    
    data class CacheEntry<T>(
        val data: T,
        val timestamp: Long,
        val ttl: Long = 5 * 60 * 1000 // 5 minutes default TTL
    ) {
        fun isExpired(): Boolean = System.currentTimeMillis() - timestamp > ttl
    }
    
    // Cache management
    fun <T> getCachedData(key: String): T? {
        val entry = cache[key] as? CacheEntry<T>
        return if (entry != null && !entry.isExpired()) {
            entry.data
        } else {
            cache.remove(key)
            null
        }
    }
    
    fun <T> setCachedData(key: String, data: T, ttl: Long = 5 * 60 * 1000) {
        cache[key] = CacheEntry(data, System.currentTimeMillis(), ttl)
    }
    
    fun clearCache() {
        cache.clear()
    }
    
    fun clearExpiredCache() {
        val expiredKeys = cache.filter { it.value.isExpired() }.keys
        expiredKeys.forEach { cache.remove(it) }
    }
    
    // Optimized spending pattern analysis
    suspend fun getOptimizedSpendingPatterns(userId: String): Flow<List<SpendingPattern>> = flow {
        val cacheKey = "spending_patterns_$userId"
        
        // Try cache first
        getCachedData<List<SpendingPattern>>(cacheKey)?.let { cachedData ->
            emit(cachedData)
            return@flow
        }
        
        // Perform analysis in background
        val patterns = PerformanceMonitor.measureTime("spending_pattern_analysis") {
            analyzeSpendingPatterns(userId)
        }
        
        // Cache results
        setCachedData(cacheKey, patterns, ttl = 10 * 60 * 1000) // 10 minutes
        emit(patterns)
    }
    
    private suspend fun analyzeSpendingPatterns(userId: String): List<SpendingPattern> {
        return withContext(Dispatchers.IO) {
            try {
                val expenses = database.expenseDao().getAllExpenses()
                val patterns = mutableListOf<SpendingPattern>()
                
                // Analyze by category
                val categoryGroups = expenses.groupBy { it.category }
                categoryGroups.forEach { (category, categoryExpenses) ->
                    val totalAmount = categoryExpenses.sumOf { it.amount }
                    val avgAmount = totalAmount / categoryExpenses.size
                    val frequency = categoryExpenses.size
                    
                    patterns.add(
                        SpendingPattern(
                            patternType = "category",
                            patternName = category,
                            frequency = frequency,
                            averageAmount = avgAmount,
                            totalAmount = totalAmount,
                            confidence = calculateConfidence(frequency, categoryExpenses.size),
                            description = "You spend an average of $${String.format("%.2f", avgAmount)} on $category",
                            recommendation = generateCategoryRecommendation(category, avgAmount, frequency),
                            timestamp = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
                        )
                    )
                }
                
                // Analyze by time patterns
                analyzeTimePatterns(expenses, patterns)
                
                patterns
            } catch (e: Exception) {
                ErrorHandler.logError("Failed to analyze spending patterns", e)
                emptyList()
            }
        }
    }
    
    private fun analyzeTimePatterns(expenses: List<Expense>, patterns: MutableList<SpendingPattern>) {
        // Analyze spending by day of week
        val dayGroups = expenses.groupBy { it.date.dayOfWeek }
        dayGroups.forEach { (dayOfWeek, dayExpenses) ->
            val totalAmount = dayExpenses.sumOf { it.amount }
            val avgAmount = totalAmount / dayExpenses.size
            
            patterns.add(
                SpendingPattern(
                    patternType = "day_of_week",
                    patternName = dayOfWeek.name,
                    frequency = dayExpenses.size,
                    averageAmount = avgAmount,
                    totalAmount = totalAmount,
                    confidence = calculateConfidence(dayExpenses.size, expenses.size),
                    description = "You tend to spend $${String.format("%.2f", avgAmount)} on ${dayOfWeek.name}s",
                    recommendation = generateTimeRecommendation(dayOfWeek.name, avgAmount),
                    timestamp = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
                )
            )
        }
    }
    
    private fun calculateConfidence(frequency: Int, totalSamples: Int): Double {
        return if (totalSamples > 0) {
            (frequency.toDouble() / totalSamples * 100).coerceAtMost(100.0)
        } else 0.0
    }
    
    private fun generateCategoryRecommendation(category: String, avgAmount: Double, frequency: Int): String {
        return when {
            avgAmount > 50 && frequency > 10 -> "Consider setting a budget limit for $category to control spending"
            frequency > 20 -> "You frequently spend on $category. Look for ways to optimize these purchases"
            else -> "Your $category spending appears reasonable"
        }
    }
    
    private fun generateTimeRecommendation(day: String, avgAmount: Double): String {
        return when {
            avgAmount > 100 -> "Consider planning purchases on $day to avoid impulse spending"
            else -> "Your spending pattern on $day looks balanced"
        }
    }
    
    // Optimized budget analytics
    suspend fun getOptimizedBudgetAnalytics(userId: String): BudgetAnalytics? {
        val cacheKey = "budget_analytics_$userId"
        
        // Try cache first
        getCachedData<BudgetAnalytics>(cacheKey)?.let { return it }
        
        return PerformanceMonitor.measureTime("budget_analytics") {
            calculateBudgetAnalytics(userId)
        }?.also { analytics ->
            setCachedData(cacheKey, analytics, ttl = 15 * 60 * 1000) // 15 minutes
        }
    }
    
    private suspend fun calculateBudgetAnalytics(userId: String): BudgetAnalytics? {
        return withContext(Dispatchers.IO) {
            try {
                val budgetCategories = database.budgetCategoryDao().getAllBudgetCategories()
                val expenses = database.expenseDao().getAllExpenses()
                
                val totalBudget = budgetCategories.sumOf { it.budgetAmount }
                val totalSpent = expenses.sumOf { it.amount }
                val remainingBudget = totalBudget - totalSpent
                
                val categoryAnalysis = budgetCategories.map { category ->
                    val categoryExpenses = expenses.filter { it.category == category.name }
                    val categorySpent = categoryExpenses.sumOf { it.amount }
                    val utilizationRate = if (category.budgetAmount > 0) {
                        (categorySpent / category.budgetAmount * 100)
                    } else 0.0
                    
                    mapOf(
                        "category" to category.name,
                        "budgeted" to category.budgetAmount,
                        "spent" to categorySpent,
                        "remaining" to (category.budgetAmount - categorySpent),
                        "utilization" to utilizationRate
                    )
                }
                
                BudgetAnalytics(
                    totalBudget = totalBudget,
                    totalSpent = totalSpent,
                    remainingBudget = remainingBudget,
                    utilizationRate = if (totalBudget > 0) (totalSpent / totalBudget * 100) else 0.0,
                    categoryBreakdown = categoryAnalysis.toString(),
                    savingsRate = if (totalBudget > 0) (remainingBudget / totalBudget * 100) else 0.0,
                    projectedEndOfMonthSpending = projectEndOfMonthSpending(expenses),
                    budgetHealthScore = calculateBudgetHealthScore(totalBudget, totalSpent, categoryAnalysis),
                    recommendations = generateBudgetRecommendations(categoryAnalysis),
                    timestamp = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
                )
            } catch (e: Exception) {
                ErrorHandler.logError("Failed to calculate budget analytics", e)
                null
            }
        }
    }
    
    private fun projectEndOfMonthSpending(expenses: List<Expense>): Double {
        if (expenses.isEmpty()) return 0.0
        
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        val currentDay = now.dayOfMonth
        val daysInMonth = 30 // Simplified
        
        val currentMonthExpenses = expenses.filter { 
            it.date.month == now.month && it.date.year == now.year 
        }
        
        val dailyAverage = currentMonthExpenses.sumOf { it.amount } / currentDay
        return dailyAverage * daysInMonth
    }
    
    private fun calculateBudgetHealthScore(
        totalBudget: Double, 
        totalSpent: Double, 
        categoryAnalysis: List<Map<String, Any>>
    ): Double {
        if (totalBudget <= 0) return 0.0
        
        val utilizationRate = totalSpent / totalBudget * 100
        val overBudgetCategories = categoryAnalysis.count { 
            (it["utilization"] as Double) > 100 
        }
        
        return when {
            utilizationRate <= 70 && overBudgetCategories == 0 -> 100.0
            utilizationRate <= 85 && overBudgetCategories <= 1 -> 80.0
            utilizationRate <= 100 && overBudgetCategories <= 2 -> 60.0
            utilizationRate <= 120 -> 40.0
            else -> 20.0
        }
    }
    
    private fun generateBudgetRecommendations(categoryAnalysis: List<Map<String, Any>>): String {
        val recommendations = mutableListOf<String>()
        
        categoryAnalysis.forEach { category ->
            val utilization = category["utilization"] as Double
            val categoryName = category["category"] as String
            
            when {
                utilization > 100 -> recommendations.add("Reduce spending in $categoryName")
                utilization > 80 -> recommendations.add("Monitor $categoryName spending closely")
                utilization < 50 -> recommendations.add("Consider reallocating budget from $categoryName")
            }
        }
        
        return recommendations.joinToString("; ")
    }
    
    // Background optimization tasks
    fun scheduleOptimizationTasks() {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
            .setRequiresBatteryNotLow(true)
            .build()
        
        val cacheCleanupWork = PeriodicWorkRequestBuilder<CacheCleanupWorker>(1, TimeUnit.HOURS)
            .setConstraints(constraints)
            .build()
        
        val analyticsPrecomputeWork = PeriodicWorkRequestBuilder<AnalyticsPrecomputeWorker>(6, TimeUnit.HOURS)
            .setConstraints(constraints)
            .build()
        
        WorkManager.getInstance(context).apply {
            enqueueUniquePeriodicWork(
                "cache_cleanup",
                ExistingPeriodicWorkPolicy.REPLACE,
                cacheCleanupWork
            )
            
            enqueueUniquePeriodicWork(
                "analytics_precompute",
                ExistingPeriodicWorkPolicy.REPLACE,
                analyticsPrecomputeWork
            )
        }
    }
    
    // Memory management
    fun optimizeMemoryUsage() {
        coroutineScope.launch {
            clearExpiredCache()
            System.gc() // Suggest garbage collection
        }
    }
    
    fun destroy() {
        coroutineScope.cancel()
        clearCache()
    }
}

// Worker classes for background optimization
class CacheCleanupWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    override suspend fun doWork(): Result {
        return try {
            // Clear expired cache entries
            // This would be injected in a real implementation
            Result.success()
        } catch (e: Exception) {
            ErrorHandler.logError("Cache cleanup failed", e)
            Result.retry()
        }
    }
}

class AnalyticsPrecomputeWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    override suspend fun doWork(): Result {
        return try {
            // Precompute analytics for faster access
            // This would be injected in a real implementation
            Result.success()
        } catch (e: Exception) {
            ErrorHandler.logError("Analytics precompute failed", e)
            Result.retry()
        }
    }
}
