1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.focusflow"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Network permissions for AI features and data sync -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- Notification permissions -->
16    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
16-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:10:5-77
16-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:10:22-74
17    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
17-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:11:5-79
17-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:11:22-76
18
19    <!-- Camera permission for receipt scanning (optional) -->
20    <uses-permission android:name="android.permission.CAMERA" />
20-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:14:5-65
20-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:14:22-62
21
22    <uses-feature
22-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:15:5-85
23        android:name="android.hardware.camera"
23-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:15:19-57
24        android:required="false" />
24-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:15:58-82
25
26    <!-- Storage permissions for receipt images -->
27    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
27-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:18:5-76
27-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:18:22-73
28    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
28-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:19:5-75
28-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:19:22-72
29    <uses-permission
29-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:20:5-21:38
30        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
30-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:20:22-78
31        android:maxSdkVersion="28" />
31-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:21:9-35
32
33    <!-- Biometric authentication -->
34    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
34-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:24:5-72
34-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:24:22-69
35
36    <!-- Voice input permissions -->
37    <uses-permission android:name="android.permission.RECORD_AUDIO" />
37-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:27:5-71
37-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:27:22-68
38
39    <uses-feature
39-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:28:5-89
40        android:name="android.hardware.microphone"
40-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:28:19-61
41        android:required="false" />
41-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:28:62-86
42
43    <!-- Accessibility services -->
44    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
44-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:31:5-32:47
44-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:31:22-82
45
46    <!-- suppress DeprecatedClassUsageInspection -->
47    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
47-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0e1dc919567705f737931e90f7aead7b\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
47-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0e1dc919567705f737931e90f7aead7b\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
48    <uses-permission android:name="android.permission.WAKE_LOCK" />
48-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
48-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:22-65
49    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
49-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
49-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
50    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
50-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
50-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:22-74
51
52    <permission
52-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\904fd36595e0135e8fdca5c15906c24d\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
53        android:name="com.focusflow.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
53-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\904fd36595e0135e8fdca5c15906c24d\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
54        android:protectionLevel="signature" />
54-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\904fd36595e0135e8fdca5c15906c24d\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
55
56    <uses-permission android:name="com.focusflow.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
56-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\904fd36595e0135e8fdca5c15906c24d\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
56-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\904fd36595e0135e8fdca5c15906c24d\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
57
58    <application
58-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:34:5-93:19
59        android:name="com.focusflow.FocusFlowApplication"
59-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:35:9-45
60        android:allowBackup="false"
60-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:36:9-36
61        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
61-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\904fd36595e0135e8fdca5c15906c24d\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
62        android:dataExtractionRules="@xml/data_extraction_rules"
62-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:37:9-65
63        android:extractNativeLibs="false"
64        android:fullBackupContent="@xml/backup_rules"
64-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:38:9-54
65        android:icon="@mipmap/ic_launcher"
65-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:39:9-43
66        android:label="@string/app_name"
66-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:40:9-41
67        android:roundIcon="@mipmap/ic_launcher_round"
67-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:41:9-54
68        android:supportsRtl="true"
68-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:42:9-35
69        android:theme="@style/Theme.FocusFlow"
69-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:43:9-47
70        android:usesCleartextTraffic="false" >
70-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:44:9-45
71        <activity
71-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:47:9-58:20
72            android:name="com.focusflow.MainActivity"
72-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:48:13-41
73            android:exported="true"
73-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:49:13-36
74            android:label="@string/app_name"
74-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:50:13-45
75            android:screenOrientation="unspecified"
75-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:52:13-52
76            android:theme="@style/Theme.FocusFlow"
76-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:51:13-51
77            android:windowSoftInputMode="adjustResize" >
77-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:53:13-55
78            <intent-filter>
78-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:54:13-57:29
79                <action android:name="android.intent.action.MAIN" />
79-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:55:17-69
79-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:55:25-66
80
81                <category android:name="android.intent.category.LAUNCHER" />
81-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:56:17-77
81-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:56:27-74
82            </intent-filter>
83        </activity>
84
85        <!-- Onboarding Activity -->
86        <activity
86-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:61:9-65:55
87            android:name="com.focusflow.ui.onboarding.OnboardingActivity"
87-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:62:13-61
88            android:exported="false"
88-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:63:13-37
89            android:screenOrientation="unspecified"
89-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:65:13-52
90            android:theme="@style/Theme.FocusFlow.NoActionBar" />
90-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:64:13-63
91
92        <!-- Notification Service -->
93        <service
93-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:68:9-70:40
94            android:name="com.focusflow.service.NotificationService"
94-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:69:13-56
95            android:exported="false" />
95-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:70:13-37
96
97        <!-- Alarm Receiver for scheduled notifications -->
98        <receiver
98-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:73:9-75:40
99            android:name="com.focusflow.receiver.AlarmReceiver"
99-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:74:13-51
100            android:exported="false" />
100-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:75:13-37
101
102        <!-- File Provider for sharing receipts -->
103        <provider
104            android:name="androidx.core.content.FileProvider"
104-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:79:13-62
105            android:authorities="com.focusflow.fileProvider"
105-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:80:13-64
106            android:exported="false"
106-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:81:13-37
107            android:grantUriPermissions="true" >
107-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:82:13-47
108            <meta-data
108-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:83:13-85:54
109                android:name="android.support.FILE_PROVIDER_PATHS"
109-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:84:17-67
110                android:resource="@xml/file_paths" />
110-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:85:17-51
111        </provider>
112
113        <!-- Network Security Config -->
114        <meta-data
114-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:89:9-91:63
115            android:name="android.security.net.config"
115-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:90:13-55
116            android:resource="@xml/network_security_config" />
116-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:91:13-60
117
118        <provider
118-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
119            android:name="androidx.startup.InitializationProvider"
119-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
120            android:authorities="com.focusflow.androidx-startup"
120-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
121            android:exported="false" >
121-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
122            <meta-data
122-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
123                android:name="androidx.work.WorkManagerInitializer"
123-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
124                android:value="androidx.startup" />
124-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
125            <meta-data
125-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fecd53a331ce0b963897f9da8c854aa\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
126                android:name="androidx.emoji2.text.EmojiCompatInitializer"
126-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fecd53a331ce0b963897f9da8c854aa\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:30:17-75
127                android:value="androidx.startup" />
127-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fecd53a331ce0b963897f9da8c854aa\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:31:17-49
128            <meta-data
128-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9fe1310f25ca312ed873f64d82bd0648\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
129                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
129-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9fe1310f25ca312ed873f64d82bd0648\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
130                android:value="androidx.startup" />
130-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9fe1310f25ca312ed873f64d82bd0648\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
131            <meta-data
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
132                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
133                android:value="androidx.startup" />
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
134        </provider>
135
136        <service
136-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
137            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
137-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
138            android:directBootAware="false"
138-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
139            android:enabled="@bool/enable_system_alarm_service_default"
139-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
140            android:exported="false" />
140-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
141        <service
141-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
142            android:name="androidx.work.impl.background.systemjob.SystemJobService"
142-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
143            android:directBootAware="false"
143-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
144            android:enabled="@bool/enable_system_job_service_default"
144-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
145            android:exported="true"
145-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
146            android:permission="android.permission.BIND_JOB_SERVICE" />
146-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
147        <service
147-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
148            android:name="androidx.work.impl.foreground.SystemForegroundService"
148-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
149            android:directBootAware="false"
149-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
150            android:enabled="@bool/enable_system_foreground_service_default"
150-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
151            android:exported="false" />
151-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
152
153        <receiver
153-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
154            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
154-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
155            android:directBootAware="false"
155-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
156            android:enabled="true"
156-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
157            android:exported="false" />
157-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
158        <receiver
158-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
159            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
159-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
160            android:directBootAware="false"
160-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
161            android:enabled="false"
161-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
162            android:exported="false" >
162-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
163            <intent-filter>
163-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
164                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
164-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
164-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
165                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
165-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
165-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
166            </intent-filter>
167        </receiver>
168        <receiver
168-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
169            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
169-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
170            android:directBootAware="false"
170-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
171            android:enabled="false"
171-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
172            android:exported="false" >
172-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
173            <intent-filter>
173-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
174                <action android:name="android.intent.action.BATTERY_OKAY" />
174-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
174-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
175                <action android:name="android.intent.action.BATTERY_LOW" />
175-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
175-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
176            </intent-filter>
177        </receiver>
178        <receiver
178-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
179            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
179-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
180            android:directBootAware="false"
180-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
181            android:enabled="false"
181-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
182            android:exported="false" >
182-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
183            <intent-filter>
183-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
184                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
184-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
184-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
185                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
185-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
185-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
186            </intent-filter>
187        </receiver>
188        <receiver
188-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
189            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
189-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
190            android:directBootAware="false"
190-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
191            android:enabled="false"
191-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
192            android:exported="false" >
192-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
193            <intent-filter>
193-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
194                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
194-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
194-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
195            </intent-filter>
196        </receiver>
197        <receiver
197-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
198            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
198-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
199            android:directBootAware="false"
199-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
200            android:enabled="false"
200-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
201            android:exported="false" >
201-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
202            <intent-filter>
202-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
203                <action android:name="android.intent.action.BOOT_COMPLETED" />
203-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
203-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
204                <action android:name="android.intent.action.TIME_SET" />
204-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
204-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
205                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
205-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
205-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
206            </intent-filter>
207        </receiver>
208        <receiver
208-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
209            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
209-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
210            android:directBootAware="false"
210-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
211            android:enabled="@bool/enable_system_alarm_service_default"
211-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
212            android:exported="false" >
212-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
213            <intent-filter>
213-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
214                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
214-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
214-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
215            </intent-filter>
216        </receiver>
217        <receiver
217-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
218            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
218-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
219            android:directBootAware="false"
219-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
220            android:enabled="true"
220-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
221            android:exported="true"
221-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
222            android:permission="android.permission.DUMP" >
222-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
223            <intent-filter>
223-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
224                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
224-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
224-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
225            </intent-filter>
226        </receiver>
227
228        <service
228-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\10a529dc4934703a517ccd294fe20872\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
229            android:name="androidx.room.MultiInstanceInvalidationService"
229-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\10a529dc4934703a517ccd294fe20872\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
230            android:directBootAware="true"
230-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\10a529dc4934703a517ccd294fe20872\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
231            android:exported="false" />
231-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\10a529dc4934703a517ccd294fe20872\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
232
233        <receiver
233-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
234            android:name="androidx.profileinstaller.ProfileInstallReceiver"
234-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
235            android:directBootAware="false"
235-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
236            android:enabled="true"
236-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
237            android:exported="true"
237-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
238            android:permission="android.permission.DUMP" >
238-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
239            <intent-filter>
239-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
240                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
240-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
240-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
241            </intent-filter>
242            <intent-filter>
242-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
243                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
243-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
243-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
244            </intent-filter>
245            <intent-filter>
245-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
246                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
246-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
246-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
247            </intent-filter>
248            <intent-filter>
248-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
249                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
249-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
249-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
250            </intent-filter>
251        </receiver>
252    </application>
253
254</manifest>
