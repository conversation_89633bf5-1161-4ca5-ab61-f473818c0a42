<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="adhd_calm_blue">#FF81C784</color>
    <color name="adhd_error_red">#FFE57373</color>
    <color name="adhd_focus_green">#FF66BB6A</color>
    <color name="adhd_success_green">#FF81C784</color>
    <color name="adhd_warning_amber">#FFFFB74D</color>
    <color name="black">#FF000000</color>
    <color name="dark_background">#FF121212</color>
    <color name="dark_background_soft">#FF1E1E1E</color>
    <color name="dark_on_surface">#FFFFFFFF</color>
    <color name="dark_surface">#FF1F1F1F</color>
    <color name="dark_text_primary">#FFFFFFFF</color>
    <color name="dark_text_secondary">#FFBDBDBD</color>
    <color name="focus_blue">#FF2196F3</color>
    <color name="focus_blue_dark">#FF1976D2</color>
    <color name="focus_blue_light">#FF64B5F6</color>
    <color name="focus_green">#FF4CAF50</color>
    <color name="focus_green_light">#FF81C784</color>
    <color name="focus_orange">#FFFF9800</color>
    <color name="gray_100">#FFF5F5F5</color>
    <color name="gray_200">#FFEEEEEE</color>
    <color name="gray_300">#FFE0E0E0</color>
    <color name="gray_400">#FFBDBDBD</color>
    <color name="gray_50">#FFFAFAFA</color>
    <color name="gray_500">#FF9E9E9E</color>
    <color name="gray_600">#FF757575</color>
    <color name="gray_700">#FF616161</color>
    <color name="gray_800">#FF424242</color>
    <color name="gray_900">#FF212121</color>
    <color name="high_contrast_accent">#FFFF0000</color>
    <color name="high_contrast_dark_accent">#FFFF4444</color>
    <color name="high_contrast_dark_background">#FF000000</color>
    <color name="high_contrast_dark_primary">#FFFFFFFF</color>
    <color name="high_contrast_dark_surface">#FF000000</color>
    <color name="high_contrast_dark_text">#FFFFFFFF</color>
    <color name="high_contrast_primary">#FF000000</color>
    <color name="high_contrast_surface">#FFFFFFFF</color>
    <color name="high_contrast_text">#FF000000</color>
    <color name="ic_launcher_background">#FF2196F3</color>
    <color name="on_surface_light">#FF000000</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="surface_light">#FFFFFFFF</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="text_primary">#FF212121</color>
    <color name="text_secondary">#FF757575</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="button_corner_radius">8dp</dimen>
    <dimen name="card_corner_radius">12dp</dimen>
    <dimen name="card_elevation">4dp</dimen>
    <dimen name="focus_border_width">3dp</dimen>
    <dimen name="focus_corner_radius">8dp</dimen>
    <dimen name="spacing_lg">24dp</dimen>
    <dimen name="spacing_md">16dp</dimen>
    <dimen name="spacing_sm">8dp</dimen>
    <dimen name="spacing_xl">32dp</dimen>
    <dimen name="spacing_xs">4dp</dimen>
    <dimen name="text_size_body">16sp</dimen>
    <dimen name="text_size_body_large">18sp</dimen>
    <dimen name="text_size_body_xl">22sp</dimen>
    <dimen name="text_size_caption">14sp</dimen>
    <dimen name="text_size_caption_large">16sp</dimen>
    <dimen name="text_size_caption_xl">20sp</dimen>
    <dimen name="text_size_headline">24sp</dimen>
    <dimen name="text_size_headline_large">28sp</dimen>
    <dimen name="text_size_headline_xl">32sp</dimen>
    <dimen name="text_size_small">12sp</dimen>
    <dimen name="text_size_title">20sp</dimen>
    <dimen name="text_size_title_large">24sp</dimen>
    <dimen name="text_size_title_xl">28sp</dimen>
    <dimen name="touch_target_comfortable">56dp</dimen>
    <dimen name="touch_target_large">64dp</dimen>
    <dimen name="touch_target_min">48dp</dimen>
    <string name="app_name">FocusFlow</string>
    <string name="default_notification_message">Time to check your tasks!</string>
    <string name="default_notification_title">FocusFlow Reminder</string>
    <string name="get_started">Get Started</string>
    <string name="notification_channel_description">Notifications for tasks, reminders and focus sessions</string>
    <string name="notification_channel_name">FocusFlow Notifications</string>
    <string name="welcome_subtitle">Your ADHD-friendly companion for better focus and productivity</string>
    <string name="welcome_title">Welcome to FocusFlow</string>
    <style name="TextAppearance.FocusFlow.Body" parent="TextAppearance.AppCompat.Body1">
        <item name="android:textSize">@dimen/text_size_body</item>
        <item name="android:lineSpacingExtra">4dp</item>
        <item name="android:fontFamily">@font/roboto_regular</item>
    </style>
    <style name="TextAppearance.FocusFlow.Headline" parent="TextAppearance.AppCompat.Headline">
        <item name="android:textSize">@dimen/text_size_headline</item>
        <item name="android:fontFamily">@font/roboto_medium</item>
        <item name="android:letterSpacing">0.02</item>
    </style>
    <style name="Theme.FocusFlow" parent="Theme.AppCompat.DayNight">
        
        <item name="colorPrimary">@color/focus_blue</item>
        <item name="colorPrimaryDark">@color/focus_blue_dark</item>
        <item name="colorAccent">@color/focus_green</item>
        
        <item name="android:statusBarColor">@color/focus_blue_dark</item>
        
        <item name="android:windowBackground">@color/white</item>
        
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        
        <item name="colorSurface">@color/surface_light</item>
        <item name="colorOnSurface">@color/on_surface_light</item>
    </style>
    <style name="Theme.FocusFlow.HighContrast" parent="Theme.FocusFlow">
        <item name="colorPrimary">@color/high_contrast_primary</item>
        <item name="colorAccent">@color/high_contrast_accent</item>
        <item name="android:textColorPrimary">@color/high_contrast_text</item>
        <item name="colorSurface">@color/high_contrast_surface</item>
    </style>
    <style name="Theme.FocusFlow.NoActionBar" parent="Theme.FocusFlow">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
</resources>