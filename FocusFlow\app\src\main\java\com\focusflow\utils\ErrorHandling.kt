package com.focusflow.utils

import android.content.Context
import android.util.Log
import androidx.compose.material.SnackbarDuration
import androidx.compose.material.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

object ErrorHandler {
    private const val TAG = "FocusFlow"

    fun logError(message: String, throwable: Throwable? = null) {
        Log.e(TAG, message, throwable)
    }

    fun logWarning(message: String) {
        Log.w(TAG, message)
    }

    fun logInfo(message: String) {
        Log.i(TAG, message)
    }

    fun getUserFriendlyMessage(error: Throwable): String {
        return when (error) {
            is java.net.UnknownHostException -> "No internet connection. Please check your network and try again."
            is java.net.SocketTimeoutException -> "Request timed out. Please try again."
            is java.io.IOException -> "Network error occurred. Please try again."
            is IllegalArgumentException -> "Invalid input provided. Please check your data and try again."
            is SecurityException -> "Permission denied. Please check app permissions."
            else -> "Something went wrong. Please try again."
        }
    }

    suspend fun showErrorSnackbar(
        snackbarHostState: SnackbarHostState,
        message: String,
        actionLabel: String? = null,
        duration: SnackbarDuration = SnackbarDuration.Short
    ) {
        snackbarHostState.showSnackbar(
            message = message,
            actionLabel = actionLabel,
            duration = duration
        )
    }
}

@Composable
fun ErrorSnackbar(
    error: String?,
    snackbarHostState: SnackbarHostState,
    onErrorShown: () -> Unit
) {
    LaunchedEffect(error) {
        error?.let {
            ErrorHandler.showErrorSnackbar(snackbarHostState, it)
            onErrorShown()
        }
    }
}

// Performance monitoring utilities
object PerformanceMonitor {
    private val performanceMetrics = mutableMapOf<String, Long>()

    fun startTimer(operation: String) {
        performanceMetrics[operation] = System.currentTimeMillis()
    }

    fun endTimer(operation: String): Long {
        val startTime = performanceMetrics[operation] ?: return 0L
        val duration = System.currentTimeMillis() - startTime
        performanceMetrics.remove(operation)
        
        if (duration > 1000) { // Log operations taking more than 1 second
            ErrorHandler.logWarning("Slow operation: $operation took ${duration}ms")
        }
        
        return duration
    }

    inline fun <T> measureTime(operation: String, block: () -> T): T {
        startTimer(operation)
        return try {
            block()
        } finally {
            endTimer(operation)
        }
    }
}

// Input validation utilities
object ValidationUtils {
    fun isValidAmount(amount: String): Boolean {
        return try {
            val value = amount.toDouble()
            value >= 0 && value <= 999999.99
        } catch (e: NumberFormatException) {
            false
        }
    }

    fun isValidEmail(email: String): Boolean {
        return android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }

    fun isValidCreditCardNumber(cardNumber: String): Boolean {
        val cleaned = cardNumber.replace("\\s".toRegex(), "")
        return cleaned.length in 13..19 && cleaned.all { it.isDigit() }
    }

    fun sanitizeInput(input: String): String {
        return input.trim().take(255) // Limit input length
    }

    fun validateBudgetAmount(amount: Double): String? {
        return when {
            amount < 0 -> "Budget amount cannot be negative"
            amount > 100000 -> "Budget amount seems too high. Please check your input."
            amount == 0.0 -> "Budget amount cannot be zero"
            else -> null
        }
    }

    fun validateExpenseAmount(amount: Double): String? {
        return when {
            amount < 0 -> "Expense amount cannot be negative"
            amount > 50000 -> "Expense amount seems very high. Please verify."
            amount == 0.0 -> "Expense amount cannot be zero"
            else -> null
        }
    }
}

// Security utilities
object SecurityUtils {
    fun sanitizeForDatabase(input: String): String {
        // Remove potential SQL injection characters
        return input.replace(Regex("[';\"\\\\]"), "")
    }

    fun isSecureInput(input: String): Boolean {
        // Check for common injection patterns
        val dangerousPatterns = listOf(
            "script", "javascript", "onload", "onerror",
            "select", "insert", "update", "delete", "drop"
        )
        
        val lowerInput = input.lowercase()
        return dangerousPatterns.none { pattern ->
            lowerInput.contains(pattern)
        }
    }
}

// Data integrity utilities
object DataIntegrityUtils {
    fun validateFinancialData(
        expenses: List<com.focusflow.data.model.Expense>,
        budgets: List<com.focusflow.data.model.BudgetCategory>
    ): List<String> {
        val issues = mutableListOf<String>()
        
        // Check for negative amounts
        expenses.forEach { expense ->
            if (expense.amount < 0) {
                issues.add("Negative expense amount found: ${expense.amount}")
            }
        }
        
        budgets.forEach { budget ->
            if (budget.allocatedAmount < 0) {
                issues.add("Negative budget amount found: ${budget.allocatedAmount}")
            }
            if (budget.spentAmount < 0) {
                issues.add("Negative spent amount found: ${budget.spentAmount}")
            }
        }
        
        // Check for unrealistic amounts
        expenses.forEach { expense ->
            if (expense.amount > 10000) {
                issues.add("Unusually high expense: $${expense.amount}")
            }
        }
        
        return issues
    }
}

// Enhanced accessibility utilities for ADHD-friendly design
object AccessibilityUtils {
    fun getContentDescription(amount: Double, context: String): String {
        val formattedAmount = String.format("%.2f", amount)
        return "$context: $formattedAmount dollars"
    }

    fun getProgressDescription(current: Double, total: Double): String {
        val percentage = if (total > 0) (current / total * 100).toInt() else 0
        return "Progress: $percentage percent complete"
    }

    fun getSpendingCategoryDescription(category: String, amount: Double, budget: Double): String {
        val percentage = if (budget > 0) (amount / budget * 100).toInt() else 0
        return "$category spending: $${String.format("%.2f", amount)} of $${String.format("%.2f", budget)} budget, $percentage percent used"
    }

    fun getDebtDescription(cardName: String, balance: Double, limit: Double): String {
        val utilization = if (limit > 0) (balance / limit * 100).toInt() else 0
        return "$cardName: $${String.format("%.2f", balance)} balance, $utilization percent utilization"
    }

    fun getFocusTimerDescription(minutes: Int, seconds: Int, isRunning: Boolean): String {
        val status = if (isRunning) "running" else "paused"
        return "Focus timer: $minutes minutes and $seconds seconds, currently $status"
    }

    fun getAchievementDescription(title: String, description: String, isUnlocked: Boolean): String {
        val status = if (isUnlocked) "unlocked" else "locked"
        return "Achievement $title: $description, currently $status"
    }

    fun getButtonDescription(action: String, context: String = ""): String {
        return if (context.isNotEmpty()) "$action button for $context" else "$action button"
    }

    fun getNavigationDescription(destination: String): String {
        return "Navigate to $destination screen"
    }

    fun getChartDescription(chartType: String, dataPoints: Int, trend: String = ""): String {
        val trendText = if (trend.isNotEmpty()) ", trend: $trend" else ""
        return "$chartType chart with $dataPoints data points$trendText"
    }

    fun getFormFieldDescription(fieldName: String, isRequired: Boolean, currentValue: String = ""): String {
        val required = if (isRequired) "required" else "optional"
        val value = if (currentValue.isNotEmpty()) ", current value: $currentValue" else ""
        return "$fieldName field, $required$value"
    }

    fun getErrorDescription(error: String): String {
        return "Error: $error. Please try again or contact support if the problem persists."
    }

    fun getSuccessDescription(action: String): String {
        return "Success: $action completed successfully"
    }

    // ADHD-specific accessibility helpers
    fun getSimplifiedDescription(complexText: String): String {
        // Simplify complex financial terms for ADHD users
        return complexText
            .replace("utilization", "usage")
            .replace("expenditure", "spending")
            .replace("allocation", "budget")
            .replace("transaction", "purchase")
            .replace("outstanding balance", "amount owed")
    }

    fun getStepByStepDescription(steps: List<String>): String {
        return "Step by step: " + steps.mapIndexed { index, step ->
            "Step ${index + 1}: $step"
        }.joinToString(". ")
    }

    fun getTimeBasedDescription(timestamp: Long): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp
        val minutes = diff / (1000 * 60)
        val hours = minutes / 60
        val days = hours / 24

        return when {
            minutes < 1 -> "just now"
            minutes < 60 -> "$minutes minutes ago"
            hours < 24 -> "$hours hours ago"
            days < 7 -> "$days days ago"
            else -> "more than a week ago"
        }
    }
}

    fun getColorDescription(color: androidx.compose.ui.graphics.Color): String {
        return when {
            color == androidx.compose.ui.graphics.Color.Green -> "Green - Good status"
            color == androidx.compose.ui.graphics.Color.Red -> "Red - Warning or over limit"
            color == androidx.compose.ui.graphics.Color(0xFFFF9800) -> "Orange - Caution"
            else -> "Status indicator"
        }
    }
}

