<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.FocusFlow" parent="Theme.AppCompat.DayNight">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/focus_blue</item>
        <item name="colorPrimaryDark">@color/focus_blue_dark</item>
        <item name="colorAccent">@color/focus_green</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/focus_blue_dark</item>
        <!-- Background colors -->
        <item name="android:windowBackground">@color/white</item>
        <!-- ADHD-friendly accessibility -->
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <!-- High contrast support -->
        <item name="colorSurface">@color/surface_light</item>
        <item name="colorOnSurface">@color/on_surface_light</item>
    </style>

    <style name="Theme.FocusFlow.NoActionBar" parent="Theme.FocusFlow">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <!-- ADHD-friendly text styles -->
    <style name="TextAppearance.FocusFlow.Headline" parent="TextAppearance.AppCompat.Headline">
        <item name="android:textSize">@dimen/text_size_headline</item>
        <item name="android:fontFamily">@font/roboto_medium</item>
        <item name="android:letterSpacing">0.02</item>
    </style>

    <style name="TextAppearance.FocusFlow.Body" parent="TextAppearance.AppCompat.Body1">
        <item name="android:textSize">@dimen/text_size_body</item>
        <item name="android:lineSpacingExtra">4dp</item>
        <item name="android:fontFamily">@font/roboto_regular</item>
    </style>

    <!-- High contrast theme -->
    <style name="Theme.FocusFlow.HighContrast" parent="Theme.FocusFlow">
        <item name="colorPrimary">@color/high_contrast_primary</item>
        <item name="colorAccent">@color/high_contrast_accent</item>
        <item name="android:textColorPrimary">@color/high_contrast_text</item>
        <item name="colorSurface">@color/high_contrast_surface</item>
    </style>
</resources>
