package com.focusflow.ui.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.focusflow.service.VoiceInputService
import com.focusflow.utils.AccessibilityUtils

@Composable
fun VoiceInputButton(
    isListening: Boolean,
    onStartListening: () -> Unit,
    onStopListening: () -> Unit,
    modifier: Modifier = Modifier,
    size: VoiceButtonSize = VoiceButtonSize.MEDIUM
) {
    val scale by animateFloatAsState(
        targetValue = if (isListening) 1.1f else 1.0f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000),
            repeatMode = RepeatMode.Reverse
        )
    )
    
    val buttonSize = when (size) {
        VoiceButtonSize.SMALL -> 48.dp
        VoiceButtonSize.MEDIUM -> 64.dp
        VoiceButtonSize.LARGE -> 80.dp
    }
    
    val iconSize = when (size) {
        VoiceButtonSize.SMALL -> 24.dp
        VoiceButtonSize.MEDIUM -> 32.dp
        VoiceButtonSize.LARGE -> 40.dp
    }
    
    Box(
        modifier = modifier
            .size(buttonSize)
            .scale(if (isListening) scale else 1f)
            .clip(CircleShape)
            .background(
                if (isListening) MaterialTheme.colors.error else MaterialTheme.colors.primary
            )
            .clickable {
                if (isListening) {
                    onStopListening()
                } else {
                    onStartListening()
                }
            }
            .semantics {
                contentDescription = AccessibilityUtils.getButtonDescription(
                    if (isListening) "Stop voice input" else "Start voice input",
                    "voice commands"
                )
            },
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = if (isListening) Icons.Default.Stop else Icons.Default.Mic,
            contentDescription = null,
            tint = Color.White,
            modifier = Modifier.size(iconSize)
        )
    }
}

@Composable
fun VoiceInputCard(
    isListening: Boolean,
    recognizedText: String,
    onStartListening: () -> Unit,
    onStopListening: () -> Unit,
    onClearText: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .semantics {
                contentDescription = AccessibilityUtils.getButtonDescription("Voice input", "speech recognition")
            },
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    imageVector = Icons.Default.RecordVoiceOver,
                    contentDescription = null,
                    tint = MaterialTheme.colors.primary,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = "Voice Commands",
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.Medium
                )
                Spacer(modifier = Modifier.weight(1f))
                VoiceInputButton(
                    isListening = isListening,
                    onStartListening = onStartListening,
                    onStopListening = onStopListening,
                    size = VoiceButtonSize.SMALL
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            if (isListening) {
                ListeningIndicator()
            } else if (recognizedText.isNotEmpty()) {
                RecognizedTextDisplay(
                    text = recognizedText,
                    onClear = onClearText
                )
            } else {
                VoiceCommandSuggestions()
            }
        }
    }
}

@Composable
fun ListeningIndicator() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.fillMaxWidth()
    ) {
        VoiceWaveAnimation()
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "Listening...",
            style = MaterialTheme.typography.body1,
            color = MaterialTheme.colors.primary,
            fontWeight = FontWeight.Medium,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "Say a command like 'Add expense' or 'Check budget'",
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f),
            textAlign = TextAlign.Center
        )
    }
}

@Composable
fun VoiceWaveAnimation() {
    val infiniteTransition = rememberInfiniteTransition()
    
    val wave1 by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(800),
            repeatMode = RepeatMode.Reverse
        )
    )
    
    val wave2 by infiniteTransition.animateFloat(
        initialValue = 0.5f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(600, delayMillis = 100),
            repeatMode = RepeatMode.Reverse
        )
    )
    
    val wave3 by infiniteTransition.animateFloat(
        initialValue = 0.7f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(700, delayMillis = 200),
            repeatMode = RepeatMode.Reverse
        )
    )
    
    Row(
        horizontalArrangement = Arrangement.spacedBy(4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        repeat(5) { index ->
            val height = when (index) {
                0, 4 -> 16.dp * wave1
                1, 3 -> 24.dp * wave2
                2 -> 32.dp * wave3
                else -> 16.dp
            }
            
            Box(
                modifier = Modifier
                    .width(4.dp)
                    .height(height)
                    .background(
                        MaterialTheme.colors.primary,
                        RoundedCornerShape(2.dp)
                    )
            )
        }
    }
}

@Composable
fun RecognizedTextDisplay(
    text: String,
    onClear: () -> Unit
) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Recognized:",
                style = MaterialTheme.typography.caption,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
            )
            
            IconButton(
                onClick = onClear,
                modifier = Modifier
                    .size(24.dp)
                    .semantics {
                        contentDescription = AccessibilityUtils.getButtonDescription("Clear recognized text")
                    }
            ) {
                Icon(
                    imageVector = Icons.Default.Clear,
                    contentDescription = null,
                    tint = MaterialTheme.colors.onSurface.copy(alpha = 0.7f),
                    modifier = Modifier.size(16.dp)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Surface(
            modifier = Modifier.fillMaxWidth(),
            color = MaterialTheme.colors.primary.copy(alpha = 0.1f),
            shape = RoundedCornerShape(8.dp)
        ) {
            Text(
                text = text,
                style = MaterialTheme.typography.body1,
                modifier = Modifier.padding(12.dp),
                color = MaterialTheme.colors.onSurface
            )
        }
    }
}

@Composable
fun VoiceCommandSuggestions() {
    Column {
        Text(
            text = "Try saying:",
            style = MaterialTheme.typography.caption,
            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        val suggestions = listOf(
            VoiceCommandSuggestion(
                command = "Add expense $25 for lunch",
                icon = Icons.Default.Receipt,
                description = "Log a new expense"
            ),
            VoiceCommandSuggestion(
                command = "Check my food budget",
                icon = Icons.Default.PieChart,
                description = "View budget status"
            ),
            VoiceCommandSuggestion(
                command = "Start 25 minute timer",
                icon = Icons.Default.Timer,
                description = "Begin focus session"
            ),
            VoiceCommandSuggestion(
                command = "Go to expenses",
                icon = Icons.Default.Navigation,
                description = "Navigate to screen"
            )
        )
        
        suggestions.forEach { suggestion ->
            VoiceCommandSuggestionItem(suggestion = suggestion)
            if (suggestion != suggestions.last()) {
                Spacer(modifier = Modifier.height(4.dp))
            }
        }
    }
}

@Composable
fun VoiceCommandSuggestionItem(
    suggestion: VoiceCommandSuggestion
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
    ) {
        Icon(
            imageVector = suggestion.icon,
            contentDescription = null,
            tint = MaterialTheme.colors.primary,
            modifier = Modifier.size(16.dp)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = "\"${suggestion.command}\"",
                style = MaterialTheme.typography.body2,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colors.onSurface
            )
            Text(
                text = suggestion.description,
                style = MaterialTheme.typography.caption,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
            )
        }
    }
}

@Composable
fun VoiceCommandResultDialog(
    result: VoiceInputService.VoiceCommandResult?,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit = {},
    onRetry: () -> Unit = {}
) {
    if (result != null) {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = if (result.isSuccessful) Icons.Default.CheckCircle else Icons.Default.Error,
                        contentDescription = null,
                        tint = if (result.isSuccessful) MaterialTheme.colors.secondary else MaterialTheme.colors.error,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = if (result.isSuccessful) "Command Processed" else "Command Failed",
                        style = MaterialTheme.typography.h6
                    )
                }
            },
            text = {
                Column {
                    Text(
                        text = "You said: \"${result.command}\"",
                        style = MaterialTheme.typography.body2,
                        fontWeight = FontWeight.Medium
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    if (result.isSuccessful && result.actionTaken != null) {
                        Text(
                            text = "Action: ${result.actionTaken}",
                            style = MaterialTheme.typography.body2,
                            color = MaterialTheme.colors.secondary
                        )
                    }
                    
                    if (result.followUpRequired && result.followUpPrompt != null) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = result.followUpPrompt,
                            style = MaterialTheme.typography.body2,
                            color = MaterialTheme.colors.primary
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "Confidence: ${(result.confidence * 100).toInt()}%",
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                }
            },
            confirmButton = {
                if (result.isSuccessful) {
                    TextButton(onClick = onConfirm) {
                        Text("OK")
                    }
                } else {
                    TextButton(onClick = onRetry) {
                        Text("Try Again")
                    }
                }
            },
            dismissButton = {
                TextButton(onClick = onDismiss) {
                    Text("Cancel")
                }
            }
        )
    }
}

enum class VoiceButtonSize {
    SMALL, MEDIUM, LARGE
}

data class VoiceCommandSuggestion(
    val command: String,
    val icon: ImageVector,
    val description: String
)
