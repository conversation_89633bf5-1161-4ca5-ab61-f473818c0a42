package com.focusflow.service

import com.focusflow.data.database.FocusFlowDatabase
import com.focusflow.data.model.*
import com.focusflow.utils.ErrorHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.datetime.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.*

@Singleton
class AdvancedAnalyticsService @Inject constructor(
    private val database: FocusFlowDatabase,
    private val performanceService: PerformanceOptimizationService
) {
    
    data class FinancialHealthScore(
        val overallScore: Double,
        val budgetScore: Double,
        val debtScore: Double,
        val savingsScore: Double,
        val spendingConsistencyScore: Double,
        val recommendations: List<String>,
        val trends: Map<String, Double>
    )
    
    data class SpendingForecast(
        val nextWeekPrediction: Double,
        val nextMonthPrediction: Double,
        val confidence: Double,
        val trendDirection: String, // "increasing", "decreasing", "stable"
        val seasonalFactors: Map<String, Double>,
        val riskFactors: List<String>
    )
    
    data class BehaviorInsight(
        val insightType: String,
        val title: String,
        val description: String,
        val impact: String, // "high", "medium", "low"
        val actionable: Boolean,
        val suggestedActions: List<String>,
        val confidence: Double
    )
    
    // Financial Health Scoring
    suspend fun calculateFinancialHealthScore(userId: String): FinancialHealthScore {
        return withContext(Dispatchers.IO) {
            try {
                val expenses = database.expenseDao().getAllExpenses()
                val creditCards = database.creditCardDao().getAllActiveCreditCards()
                val budgetCategories = database.budgetCategoryDao().getAllBudgetCategories()
                
                val budgetScore = calculateBudgetScore(expenses, budgetCategories)
                val debtScore = calculateDebtScore(creditCards)
                val savingsScore = calculateSavingsScore(expenses, budgetCategories)
                val consistencyScore = calculateSpendingConsistencyScore(expenses)
                
                val overallScore = (budgetScore + debtScore + savingsScore + consistencyScore) / 4
                
                val recommendations = generateHealthRecommendations(
                    budgetScore, debtScore, savingsScore, consistencyScore
                )
                
                val trends = calculateHealthTrends(expenses, creditCards)
                
                FinancialHealthScore(
                    overallScore = overallScore,
                    budgetScore = budgetScore,
                    debtScore = debtScore,
                    savingsScore = savingsScore,
                    spendingConsistencyScore = consistencyScore,
                    recommendations = recommendations,
                    trends = trends
                )
            } catch (e: Exception) {
                ErrorHandler.logError("Failed to calculate financial health score", e)
                FinancialHealthScore(0.0, 0.0, 0.0, 0.0, 0.0, emptyList(), emptyMap())
            }
        }
    }
    
    private fun calculateBudgetScore(expenses: List<Expense>, budgetCategories: List<BudgetCategory>): Double {
        if (budgetCategories.isEmpty()) return 50.0 // Neutral score if no budget set
        
        val totalBudget = budgetCategories.sumOf { it.budgetAmount }
        val totalSpent = expenses.sumOf { it.amount }
        
        val utilizationRate = if (totalBudget > 0) totalSpent / totalBudget else 0.0
        
        return when {
            utilizationRate <= 0.7 -> 100.0
            utilizationRate <= 0.85 -> 80.0
            utilizationRate <= 1.0 -> 60.0
            utilizationRate <= 1.2 -> 30.0
            else -> 10.0
        }
    }
    
    private fun calculateDebtScore(creditCards: List<CreditCard>): Double {
        if (creditCards.isEmpty()) return 100.0 // No debt is good
        
        val totalDebt = creditCards.sumOf { it.currentBalance }
        val totalLimit = creditCards.sumOf { it.creditLimit }
        
        if (totalLimit <= 0) return 50.0
        
        val utilizationRate = totalDebt / totalLimit
        
        return when {
            utilizationRate <= 0.1 -> 100.0
            utilizationRate <= 0.3 -> 80.0
            utilizationRate <= 0.5 -> 60.0
            utilizationRate <= 0.7 -> 40.0
            utilizationRate <= 0.9 -> 20.0
            else -> 10.0
        }
    }
    
    private fun calculateSavingsScore(expenses: List<Expense>, budgetCategories: List<BudgetCategory>): Double {
        val totalBudget = budgetCategories.sumOf { it.budgetAmount }
        val totalSpent = expenses.sumOf { it.amount }
        
        if (totalBudget <= 0) return 50.0
        
        val savingsRate = (totalBudget - totalSpent) / totalBudget
        
        return when {
            savingsRate >= 0.2 -> 100.0
            savingsRate >= 0.1 -> 80.0
            savingsRate >= 0.05 -> 60.0
            savingsRate >= 0.0 -> 40.0
            else -> 20.0
        }
    }
    
    private fun calculateSpendingConsistencyScore(expenses: List<Expense>): Double {
        if (expenses.size < 7) return 50.0 // Not enough data
        
        val dailySpending = expenses.groupBy { it.date.date }
            .mapValues { it.value.sumOf { expense -> expense.amount } }
        
        val amounts = dailySpending.values.toList()
        val mean = amounts.average()
        val variance = amounts.map { (it - mean).pow(2) }.average()
        val standardDeviation = sqrt(variance)
        
        val coefficientOfVariation = if (mean > 0) standardDeviation / mean else 0.0
        
        return when {
            coefficientOfVariation <= 0.3 -> 100.0
            coefficientOfVariation <= 0.5 -> 80.0
            coefficientOfVariation <= 0.7 -> 60.0
            coefficientOfVariation <= 1.0 -> 40.0
            else -> 20.0
        }
    }
    
    private fun generateHealthRecommendations(
        budgetScore: Double,
        debtScore: Double,
        savingsScore: Double,
        consistencyScore: Double
    ): List<String> {
        val recommendations = mutableListOf<String>()
        
        if (budgetScore < 60) {
            recommendations.add("Review and adjust your budget to better match your spending patterns")
        }
        
        if (debtScore < 60) {
            recommendations.add("Focus on paying down high-interest debt to improve your financial health")
        }
        
        if (savingsScore < 60) {
            recommendations.add("Try to increase your savings rate by reducing discretionary spending")
        }
        
        if (consistencyScore < 60) {
            recommendations.add("Work on creating more consistent spending habits to improve financial stability")
        }
        
        return recommendations
    }
    
    private fun calculateHealthTrends(expenses: List<Expense>, creditCards: List<CreditCard>): Map<String, Double> {
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        val lastMonth = now.minus(DateTimePeriod(months = 1))
        
        val recentExpenses = expenses.filter { it.date >= lastMonth }
        val olderExpenses = expenses.filter { it.date < lastMonth }
        
        val recentSpending = recentExpenses.sumOf { it.amount }
        val olderSpending = olderExpenses.sumOf { it.amount }
        
        val spendingTrend = if (olderSpending > 0) {
            ((recentSpending - olderSpending) / olderSpending) * 100
        } else 0.0
        
        val debtTrend = creditCards.sumOf { it.currentBalance } // Simplified
        
        return mapOf(
            "spending_trend" to spendingTrend,
            "debt_trend" to debtTrend
        )
    }
    
    // Spending Forecasting
    suspend fun generateSpendingForecast(userId: String): SpendingForecast {
        return withContext(Dispatchers.IO) {
            try {
                val expenses = database.expenseDao().getAllExpenses()
                
                if (expenses.size < 14) {
                    return@withContext SpendingForecast(
                        nextWeekPrediction = 0.0,
                        nextMonthPrediction = 0.0,
                        confidence = 0.0,
                        trendDirection = "insufficient_data",
                        seasonalFactors = emptyMap(),
                        riskFactors = listOf("Not enough historical data for accurate prediction")
                    )
                }
                
                val dailySpending = calculateDailySpending(expenses)
                val weeklyAverage = calculateWeeklyAverage(dailySpending)
                val monthlyAverage = calculateMonthlyAverage(dailySpending)
                
                val trend = calculateSpendingTrend(dailySpending)
                val seasonalFactors = calculateSeasonalFactors(expenses)
                
                val nextWeekPrediction = weeklyAverage * (1 + trend)
                val nextMonthPrediction = monthlyAverage * (1 + trend)
                
                val confidence = calculateForecastConfidence(expenses, trend)
                val riskFactors = identifyRiskFactors(expenses, trend)
                
                SpendingForecast(
                    nextWeekPrediction = nextWeekPrediction,
                    nextMonthPrediction = nextMonthPrediction,
                    confidence = confidence,
                    trendDirection = when {
                        trend > 0.05 -> "increasing"
                        trend < -0.05 -> "decreasing"
                        else -> "stable"
                    },
                    seasonalFactors = seasonalFactors,
                    riskFactors = riskFactors
                )
            } catch (e: Exception) {
                ErrorHandler.logError("Failed to generate spending forecast", e)
                SpendingForecast(0.0, 0.0, 0.0, "error", emptyMap(), listOf("Error generating forecast"))
            }
        }
    }
    
    private fun calculateDailySpending(expenses: List<Expense>): Map<LocalDate, Double> {
        return expenses.groupBy { it.date.date }
            .mapValues { it.value.sumOf { expense -> expense.amount } }
    }
    
    private fun calculateWeeklyAverage(dailySpending: Map<LocalDate, Double>): Double {
        val weeklyTotals = dailySpending.entries.groupBy { 
            // Group by week
            val date = it.key
            val dayOfYear = date.dayOfYear
            dayOfYear / 7
        }.mapValues { it.value.sumOf { entry -> entry.value } }
        
        return weeklyTotals.values.average()
    }
    
    private fun calculateMonthlyAverage(dailySpending: Map<LocalDate, Double>): Double {
        val monthlyTotals = dailySpending.entries.groupBy { 
            "${it.key.year}-${it.key.monthNumber}"
        }.mapValues { it.value.sumOf { entry -> entry.value } }
        
        return monthlyTotals.values.average()
    }
    
    private fun calculateSpendingTrend(dailySpending: Map<LocalDate, Double>): Double {
        val sortedEntries = dailySpending.entries.sortedBy { it.key }
        if (sortedEntries.size < 7) return 0.0
        
        val recentWeek = sortedEntries.takeLast(7).sumOf { it.value }
        val previousWeek = sortedEntries.dropLast(7).takeLast(7).sumOf { it.value }
        
        return if (previousWeek > 0) {
            (recentWeek - previousWeek) / previousWeek
        } else 0.0
    }
    
    private fun calculateSeasonalFactors(expenses: List<Expense>): Map<String, Double> {
        val monthlySpending = expenses.groupBy { it.date.month }
            .mapValues { it.value.sumOf { expense -> expense.amount } }
        
        val averageMonthly = monthlySpending.values.average()
        
        return monthlySpending.mapKeys { it.key.name }
            .mapValues { if (averageMonthly > 0) it.value / averageMonthly else 1.0 }
    }
    
    private fun calculateForecastConfidence(expenses: List<Expense>, trend: Double): Double {
        val dataPoints = expenses.size
        val trendStability = 1.0 - abs(trend).coerceAtMost(1.0)
        
        return when {
            dataPoints >= 30 && trendStability > 0.8 -> 0.9
            dataPoints >= 20 && trendStability > 0.6 -> 0.7
            dataPoints >= 14 && trendStability > 0.4 -> 0.5
            else -> 0.3
        }
    }
    
    private fun identifyRiskFactors(expenses: List<Expense>, trend: Double): List<String> {
        val riskFactors = mutableListOf<String>()
        
        if (trend > 0.2) {
            riskFactors.add("Rapidly increasing spending trend detected")
        }
        
        val recentLargeExpenses = expenses.filter { 
            it.amount > 100 && it.date >= Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).minus(DateTimePeriod(days = 7))
        }
        
        if (recentLargeExpenses.size > 3) {
            riskFactors.add("Multiple large expenses in recent week")
        }
        
        val categoryConcentration = expenses.groupBy { it.category }
            .mapValues { it.value.sumOf { expense -> expense.amount } }
        
        val totalSpending = expenses.sumOf { it.amount }
        val maxCategorySpending = categoryConcentration.values.maxOrNull() ?: 0.0
        
        if (totalSpending > 0 && maxCategorySpending / totalSpending > 0.6) {
            riskFactors.add("High concentration of spending in single category")
        }
        
        return riskFactors
    }
    
    // Behavior Insights
    suspend fun generateBehaviorInsights(userId: String): List<BehaviorInsight> {
        return withContext(Dispatchers.IO) {
            try {
                val expenses = database.expenseDao().getAllExpenses()
                val insights = mutableListOf<BehaviorInsight>()
                
                // Analyze spending patterns
                insights.addAll(analyzeSpendingPatterns(expenses))
                
                // Analyze timing patterns
                insights.addAll(analyzeTimingPatterns(expenses))
                
                // Analyze amount patterns
                insights.addAll(analyzeAmountPatterns(expenses))
                
                insights.sortedByDescending { it.confidence }
            } catch (e: Exception) {
                ErrorHandler.logError("Failed to generate behavior insights", e)
                emptyList()
            }
        }
    }
    
    private fun analyzeSpendingPatterns(expenses: List<Expense>): List<BehaviorInsight> {
        val insights = mutableListOf<BehaviorInsight>()
        
        // Weekend vs weekday spending
        val weekendExpenses = expenses.filter { 
            it.date.dayOfWeek == DayOfWeek.SATURDAY || it.date.dayOfWeek == DayOfWeek.SUNDAY 
        }
        val weekdayExpenses = expenses.filter { 
            it.date.dayOfWeek !in listOf(DayOfWeek.SATURDAY, DayOfWeek.SUNDAY)
        }
        
        if (weekendExpenses.isNotEmpty() && weekdayExpenses.isNotEmpty()) {
            val weekendAvg = weekendExpenses.sumOf { it.amount } / weekendExpenses.size
            val weekdayAvg = weekdayExpenses.sumOf { it.amount } / weekdayExpenses.size
            
            if (weekendAvg > weekdayAvg * 1.5) {
                insights.add(
                    BehaviorInsight(
                        insightType = "spending_pattern",
                        title = "Weekend Spending Spike",
                        description = "You spend significantly more on weekends (${String.format("%.2f", weekendAvg)}) compared to weekdays (${String.format("%.2f", weekdayAvg)})",
                        impact = "medium",
                        actionable = true,
                        suggestedActions = listOf(
                            "Set a weekend spending budget",
                            "Plan weekend activities in advance",
                            "Use the purchase delay feature for weekend impulse buys"
                        ),
                        confidence = 0.8
                    )
                )
            }
        }
        
        return insights
    }
    
    private fun analyzeTimingPatterns(expenses: List<Expense>): List<BehaviorInsight> {
        val insights = mutableListOf<BehaviorInsight>()
        
        // Late night spending (if we had time data)
        // For now, we'll analyze by day patterns
        val daySpending = expenses.groupBy { it.date.dayOfWeek }
            .mapValues { it.value.sumOf { expense -> expense.amount } }
        
        val maxSpendingDay = daySpending.maxByOrNull { it.value }
        if (maxSpendingDay != null && daySpending.size >= 5) {
            val avgOtherDays = daySpending.filter { it.key != maxSpendingDay.key }.values.average()
            
            if (maxSpendingDay.value > avgOtherDays * 2) {
                insights.add(
                    BehaviorInsight(
                        insightType = "timing_pattern",
                        title = "High Spending Day Pattern",
                        description = "You consistently spend more on ${maxSpendingDay.key.name}s",
                        impact = "medium",
                        actionable = true,
                        suggestedActions = listOf(
                            "Set alerts for ${maxSpendingDay.key.name} spending",
                            "Review what triggers spending on this day",
                            "Plan ${maxSpendingDay.key.name} activities with a budget"
                        ),
                        confidence = 0.7
                    )
                )
            }
        }
        
        return insights
    }
    
    private fun analyzeAmountPatterns(expenses: List<Expense>): List<BehaviorInsight> {
        val insights = mutableListOf<BehaviorInsight>()
        
        // Small frequent purchases vs large occasional ones
        val smallExpenses = expenses.filter { it.amount < 20 }
        val largeExpenses = expenses.filter { it.amount > 100 }
        
        if (smallExpenses.size > expenses.size * 0.7) {
            insights.add(
                BehaviorInsight(
                    insightType = "amount_pattern",
                    title = "Frequent Small Purchases",
                    description = "Most of your expenses (${smallExpenses.size}/${expenses.size}) are small amounts under $20",
                    impact = "low",
                    actionable = true,
                    suggestedActions = listOf(
                        "Track these small expenses more carefully",
                        "Consider if these add up to significant amounts",
                        "Set daily spending limits for small purchases"
                    ),
                    confidence = 0.9
                )
            )
        }
        
        return insights
    }
}
