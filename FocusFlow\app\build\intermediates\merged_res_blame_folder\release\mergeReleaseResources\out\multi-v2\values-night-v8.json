{"logs": [{"outputFile": "com.focusflow.app-mergeReleaseResources-65:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\Downloads\\Understanding Content in pasted_content (1)\\FocusFlow\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "37,30,3,45,24", "startColumns": "4,4,4,4,4", "startOffsets": "1944,1566,140,2370,1320", "endLines": "42,35,22,51,27", "endColumns": "12,12,12,12,12", "endOffsets": "2304,1934,1310,2848,1497"}, "to": {"startLines": "2,8,21,41,48", "startColumns": "4,4,4,4,4", "startOffsets": "55,420,1425,2352,2835", "endLines": "7,13,40,47,51", "endColumns": "12,12,12,12,12", "endOffsets": "415,788,2347,2830,3012"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b7575953e232ac886e0021593ed04f20\\transformed\\appcompat-1.6.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "14,15,16,17,18,19,20,52", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "793,863,947,1031,1127,1229,1331,3017", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "858,942,1026,1122,1224,1326,1420,3101"}}]}]}