<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Dark theme for FocusFlow -->
    <style name="Theme.FocusFlow" parent="Theme.AppCompat.DayNight">
        <!-- Primary brand color - adjusted for dark mode -->
        <item name="colorPrimary">@color/focus_blue_light</item>
        <item name="colorPrimaryDark">@color/focus_blue</item>
        <item name="colorAccent">@color/focus_green_light</item>
        <!-- Status bar color -->
        <item name="android:statusBarColor">@color/dark_surface</item>
        <!-- Background colors -->
        <item name="android:windowBackground">@color/dark_background</item>
        <!-- ADHD-friendly dark mode colors -->
        <item name="android:textColorPrimary">@color/dark_text_primary</item>
        <item name="android:textColorSecondary">@color/dark_text_secondary</item>
        <!-- Surface colors for cards and components -->
        <item name="colorSurface">@color/dark_surface</item>
        <item name="colorOnSurface">@color/dark_on_surface</item>
        <!-- Navigation bar -->
        <item name="android:navigationBarColor">@color/dark_surface</item>
        <!-- Reduced eye strain colors -->
        <item name="android:colorBackground">@color/dark_background_soft</item>
    </style>
    
    <style name="Theme.FocusFlow.NoActionBar" parent="Theme.FocusFlow">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    
    <!-- Dark mode text styles with better readability -->
    <style name="TextAppearance.FocusFlow.Headline" parent="TextAppearance.AppCompat.Headline">
        <item name="android:textSize">@dimen/text_size_headline</item>
        <item name="android:fontFamily">@font/roboto_medium</item>
        <item name="android:letterSpacing">0.02</item>
        <item name="android:textColor">@color/dark_text_primary</item>
    </style>
    
    <style name="TextAppearance.FocusFlow.Body" parent="TextAppearance.AppCompat.Body1">
        <item name="android:textSize">@dimen/text_size_body</item>
        <item name="android:lineSpacingExtra">4dp</item>
        <item name="android:fontFamily">@font/roboto_regular</item>
        <item name="android:textColor">@color/dark_text_primary</item>
    </style>
    
    <!-- High contrast dark theme for accessibility -->
    <style name="Theme.FocusFlow.HighContrast" parent="Theme.FocusFlow">
        <item name="colorPrimary">@color/high_contrast_dark_primary</item>
        <item name="colorAccent">@color/high_contrast_dark_accent</item>
        <item name="android:textColorPrimary">@color/high_contrast_dark_text</item>
        <item name="colorSurface">@color/high_contrast_dark_surface</item>
        <item name="android:windowBackground">@color/high_contrast_dark_background</item>
    </style>
</resources>
