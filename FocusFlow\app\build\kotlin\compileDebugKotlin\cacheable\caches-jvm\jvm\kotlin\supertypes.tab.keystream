"com.focusflow.FocusFlowApplicationcom.focusflow.MainActivity-com.focusflow.data.database.FocusFlowDatabase)com.focusflow.navigation.Screen.Dashboard(com.focusflow.navigation.Screen.Expenses$com.focusflow.navigation.Screen.Debt&com.focusflow.navigation.Screen.Budget&com.focusflow.navigation.Screen.Habits%com.focusflow.navigation.Screen.Tasks'com.focusflow.navigation.Screen.AICoach(com.focusflow.navigation.Screen.Settings$com.focusflow.receiver.AlarmReceiver com.focusflow.service.TimerState.com.focusflow.ui.onboarding.OnboardingActivity+com.focusflow.ui.viewmodel.AICoachViewModel*com.focusflow.ui.viewmodel.BudgetViewModel-com.focusflow.ui.viewmodel.DashboardViewModel(com.focusflow.ui.viewmodel.DebtViewModel)com.focusflow.ui.viewmodel.PayoffStrategy2com.focusflow.ui.viewmodel.EnhancedBudgetViewModel+com.focusflow.ui.viewmodel.ExpenseViewModel2com.focusflow.ui.viewmodel.ImpulseControlViewModel(com.focusflow.ui.viewmodel.MainViewModel.com.focusflow.ui.viewmodel.OnboardingViewModel)com.focusflow.ui.viewmodel.OnboardingStep,com.focusflow.ui.viewmodel.SettingsViewModel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         